#!/bin/bash

function cleanup {
    echo "Caught SIGINT, cleaning up..."
    pkill -P $$
    exit
}

trap cleanup SIGINT

# This will first kill all ports which have been forwarded through kubectl cmd. This is for avoiding port conflicts.
pkill -f "kubectl port-forward";

# These commands will forward ports in the background.
# If you need to forward to specific port, then use pod/<pod-name> instead of svc/<service-name>-internal

echo "1. Forwarding port 13101 to mozart..."
kubectl port-forward svc/mozart-internal 13101:8080 -n mozart &

echo "2. Forwarding port 13102 to neo..."
kubectl port-forward svc/neo-internal 13102:8080 -n neo &

echo "3. Forwarding port 13103 to maverick..."
kubectl port-forward svc/maverick-internal 13103:35000 -n maverick &

echo "4. Forwarding port 13104 to iris"
kubectl port-forward svc/iris-internal 13104:3010 -n iris &

echo "5. Forwarding port 13105 to odin"
kubectl port-forward svc/odin-internal 13105:5342 -n odin &

echo "6. Forwarding port 13106 to identity"
kubectl port-forward svc/identity-internal 13106:39000 -n identity &

echo "7. Forwarding port 13107 to watchmen"
kubectl port-forward svc/watchmen-internal 13107:8080 -n watchmen &

echo "8. Forwarding port 13108 to cult-api"
kubectl port-forward svc/cult-api-internal 13108:5000 -n cult-api &

echo "9. Forwarding port 13109 to shortloop"
kubectl port-forward svc/shortloop-internal 13109:80 -n shortloop &

echo 'Process running. Ports being forwarded in background. No need to exit/re-start this for application code changes.'
echo 'To exit and kill all background processes and forwarded ports, Press Ctrl+C.'

# Wait indefinitely, until Ctrl+C is pressed
while :
do
    sleep 1
done
